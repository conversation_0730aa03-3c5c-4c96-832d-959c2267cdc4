# V-Switch: Distributed Network Switching System

V-Switch is a distributed network switching system with ETCD-based coordination, shard management, agent registration, and network configuration capabilities.

## Architecture

The system consists of three main components:

1. **API Server**: Provides REST API endpoints for network operations
2. **Core Service**: Manages shards, agents, and network configurations
3. **Agent**: Executes network commands on target hosts

## Features

- **Distributed Architecture**: Uses ETCD for coordination and state management
- **Shard-based Load Distribution**: Distributes workload across multiple agents using hash-based sharding
- **Agent Registration**: Automatic agent registration with heartbeat mechanism
- **Network Operations**: Support for subnet gateway and EIP creation
- **Command Templates**: Template-based network command generation
- **Environment Checking**: Validates system requirements before execution
- **Dry-run Mode**: Test mode for command validation without execution

## Requirements

- Python 3.8 or higher
- pip (Python package installer)

## Installation

### For Development

1. Clone the repository:
```bash
git clone http://*************/nj-projects/cloud-link/v-switch.git
cd v-switch
```

2. Create a virtual environment:
```bash
python -m venv venv
```

3. Activate the virtual environment:
```bash
# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

4. Install development dependencies:
```bash
pip install -r requirements-dev.txt
```

5. Install the package in development mode:
```bash
pip install -e .
```

### For Production

```bash
pip install v-switch
```

## Usage

### Basic Usage

```python
from v_switch.main import main

# Run the main function
main()
```

### Command Line

```bash
python -m v_switch.main
```

## Development

### Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=v_switch

# Run tests with coverage report
pytest --cov=v_switch --cov-report=html
```

### Code Quality

```bash
# Format code with Black
black src/ tests/

# Lint code with flake8
flake8 src/ tests/

# Type check with mypy
mypy src/
```

### Project Structure

```
v-switch/
├── src/
│   └── v_switch/
│       ├── __init__.py
│       └── main.py
├── tests/
│   ├── __init__.py
│   └── test_main.py
├── pyproject.toml
├── requirements.txt
├── requirements-dev.txt
├── .gitignore
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and ensure they pass
5. Run code quality checks
6. Commit your changes (`git commit -m 'Add some amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Authors

- Your Name - Initial work

## Project Status

This project is in active development. Feel free to contribute or report issues.
