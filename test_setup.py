#!/usr/bin/env python3
"""
Test script to verify v-switch setup.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from v_switch.config.agent_config import AgentConfig
        print("✓ AgentConfig import successful")
        
        from v_switch.config.server_config import ServerConfig
        print("✓ ServerConfig import successful")
        
        from v_switch.common.etcd_client import ETCDClient
        print("✓ ETCDClient import successful")
        
        from v_switch.agent.agent import VSwitchAgent
        print("✓ VSwitchAgent import successful")
        
        from v_switch.core_service.core_service import CoreService
        print("✓ CoreService import successful")
        
        from v_switch.api_server.api_server import APIServer
        print("✓ APIServer import successful")
        
        from v_switch.templates.command_templates import CommandTemplates
        print("✓ CommandTemplates import successful")
        
        from v_switch.utils.logging_utils import setup_logging
        print("✓ Logging utils import successful")
        
        from v_switch.utils.validation_utils import ValidationUtils
        print("✓ Validation utils import successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


def test_config_loading():
    """Test configuration loading."""
    print("\nTesting configuration loading...")
    
    try:
        from v_switch.config.agent_config import AgentConfig
        
        # Test agent config
        if os.path.exists("config/agent_config.yaml"):
            config = AgentConfig.from_file("config/agent_config.yaml")
            print(f"✓ Agent config loaded: {config.agent.agent_id}")
            print(f"  ETCD: {config.etcd.host}:{config.etcd.port}")
            print(f"  Check env: {config.agent.check_env}")
        else:
            print("✗ Agent config file not found")
            return False
        
        from v_switch.config.server_config import ServerConfig
        
        # Test server config
        if os.path.exists("config/server_config.yaml"):
            server_config = ServerConfig.from_file("config/server_config.yaml")
            print(f"✓ Server config loaded: port {server_config.server.port}")
            print(f"  Shard count: {server_config.server.shard_count}")
        else:
            print("✗ Server config file not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False


def test_agent_creation():
    """Test agent creation."""
    print("\nTesting agent creation...")
    
    try:
        from v_switch.config.agent_config import AgentConfig
        from v_switch.agent.agent import VSwitchAgent
        
        config = AgentConfig.from_file("config/agent_config.yaml")
        agent = VSwitchAgent(config, dry_run=True)
        print("✓ Agent created successfully in dry-run mode")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        return False


def test_etcd_client():
    """Test ETCD client creation."""
    print("\nTesting ETCD client...")
    
    try:
        from v_switch.common.etcd_client import ETCDClient
        
        client = ETCDClient(host="localhost", port=2379)
        print("✓ ETCD client created")
        
        # Test connection (should work in mock mode)
        client.connect()
        print("✓ ETCD client connected (mock mode)")
        
        # Test basic operations
        result = client.put("test_key", "test_value")
        print(f"✓ ETCD put operation: {result}")
        
        value, metadata = client.get("test_key")
        print(f"✓ ETCD get operation: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ ETCD client test failed: {e}")
        return False


def test_command_templates():
    """Test command templates."""
    print("\nTesting command templates...")
    
    try:
        from v_switch.templates.command_templates import CommandTemplates
        
        # Test subnet gateway commands
        commands = CommandTemplates.get_subnet_gateway_commands("100", "*************")
        print(f"✓ Subnet gateway commands generated: {len(commands)} commands")
        
        # Test EIP commands
        eip_commands = CommandTemplates.get_eip_commands("100", "**********", "********", "*************0")
        print(f"✓ EIP commands generated: {len(eip_commands)} commands")
        
        # Test template generation
        template_result = CommandTemplates.generate_commands("subnet_gateway", {
            "vlan_id": "100",
            "subnet_gw_ip": "*************"
        })
        print(f"✓ Template generation: {len(template_result['commands'])} commands, {len(template_result['revocation'])} revocation commands")
        
        return True
        
    except Exception as e:
        print(f"✗ Command templates test failed: {e}")
        return False


def main():
    """Main test function."""
    print("V-Switch Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_loading,
        test_etcd_client,
        test_command_templates,
        test_agent_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test error: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! V-Switch setup is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
