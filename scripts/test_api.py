#!/usr/bin/env python3
"""
Test v-switch API endpoints.
"""

import json
import requests
import argparse
import time
from typing import Dict, Any


def test_health_check(base_url: str) -> bool:
    """Test health check endpoint.
    
    Args:
        base_url: Base URL of the API server
        
    Returns:
        True if successful
    """
    try:
        print("Testing health check...")
        response = requests.get(f"{base_url}/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False


def test_server_status(base_url: str) -> bool:
    """Test server status endpoint.
    
    Args:
        base_url: Base URL of the API server
        
    Returns:
        True if successful
    """
    try:
        print("Testing server status...")
        response = requests.get(f"{base_url}/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Server status: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"✗ Server status failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Server status error: {e}")
        return False


def test_create_subnet_gateway(base_url: str, tenant_id: str, vlan_id: str, subnet_gw_ip: str) -> str:
    """Test create subnet gateway endpoint.
    
    Args:
        base_url: Base URL of the API server
        tenant_id: Tenant identifier
        vlan_id: VLAN identifier
        subnet_gw_ip: Subnet gateway IP
        
    Returns:
        Request ID if successful, empty string otherwise
    """
    try:
        print(f"Testing create subnet gateway for tenant {tenant_id}, VLAN {vlan_id}...")
        
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "subnet_gw_ip": subnet_gw_ip
        }
        
        response = requests.post(
            f"{base_url}/network/subnet-gateway",
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            request_id = result.get("request_id", "")
            print(f"✓ Subnet gateway creation submitted: {result}")
            return request_id
        else:
            print(f"✗ Subnet gateway creation failed: {response.status_code} - {response.text}")
            return ""
            
    except Exception as e:
        print(f"✗ Subnet gateway creation error: {e}")
        return ""


def test_create_eip(base_url: str, tenant_id: str, vlan_id: str, eip: str, 
                   gateway_ip: str, internal_ip: str) -> str:
    """Test create EIP endpoint.
    
    Args:
        base_url: Base URL of the API server
        tenant_id: Tenant identifier
        vlan_id: VLAN identifier
        eip: External IP address
        gateway_ip: Gateway IP address
        internal_ip: Internal IP address
        
    Returns:
        Request ID if successful, empty string otherwise
    """
    try:
        print(f"Testing create EIP for tenant {tenant_id}, VLAN {vlan_id}...")
        
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "eip": eip,
            "gateway_ip": gateway_ip,
            "internal_ip": internal_ip
        }
        
        response = requests.post(
            f"{base_url}/network/eip",
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            request_id = result.get("request_id", "")
            print(f"✓ EIP creation submitted: {result}")
            return request_id
        else:
            print(f"✗ EIP creation failed: {response.status_code} - {response.text}")
            return ""
            
    except Exception as e:
        print(f"✗ EIP creation error: {e}")
        return ""


def test_get_instruction_status(base_url: str, request_id: str, tenant_id: str) -> bool:
    """Test get instruction status endpoint.
    
    Args:
        base_url: Base URL of the API server
        request_id: Request identifier
        tenant_id: Tenant identifier
        
    Returns:
        True if successful
    """
    try:
        print(f"Testing get instruction status for request {request_id}...")
        
        response = requests.get(
            f"{base_url}/instruction/{request_id}?tenant_id={tenant_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Instruction status: {json.dumps(data, indent=2)}")
            return True
        elif response.status_code == 404:
            print(f"✓ Instruction not found (expected): {response.json()}")
            return True
        else:
            print(f"✗ Get instruction status failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Get instruction status error: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test v-switch API")
    parser.add_argument(
        "--host",
        default="localhost",
        help="API server host"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="API server port"
    )
    parser.add_argument(
        "--tenant-id",
        default="test-tenant",
        help="Tenant ID for testing"
    )
    
    args = parser.parse_args()
    
    base_url = f"http://{args.host}:{args.port}"
    
    print(f"Testing v-switch API at {base_url}")
    print("=" * 50)
    
    # Test basic endpoints
    if not test_health_check(base_url):
        print("Health check failed, server may not be running")
        return 1
    
    if not test_server_status(base_url):
        print("Server status check failed")
        return 1
    
    # Test network operations
    print("\nTesting network operations...")
    print("-" * 30)
    
    # Test subnet gateway creation
    subnet_request_id = test_create_subnet_gateway(
        base_url, 
        args.tenant_id, 
        "100", 
        "*************"
    )
    
    # Test EIP creation
    eip_request_id = test_create_eip(
        base_url,
        args.tenant_id,
        "100",
        "********00",
        "********",
        "**************"
    )
    
    # Wait a bit for processing
    if subnet_request_id or eip_request_id:
        print("\nWaiting 2 seconds for processing...")
        time.sleep(2)
    
    # Test instruction status
    if subnet_request_id:
        test_get_instruction_status(base_url, subnet_request_id, args.tenant_id)
    
    if eip_request_id:
        test_get_instruction_status(base_url, eip_request_id, args.tenant_id)
    
    print("\nAPI testing completed!")
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
