# Production dependencies for v-switch agent

# Optional dependencies for enhanced functionality
# Install with: pip install -r requirements.txt

# Core dependencies (always required)
# None - the agent works with Python standard library only

# Optional dependencies for enhanced features:
# traitlets>=5.0.0    # Enhanced configuration with type checking
# etcd3>=0.12.0       # ETCD client for distributed coordination
# PyYAML>=6.0         # YAML configuration file support

# To install all optional dependencies:
# pip install traitlets etcd3 PyYAML
