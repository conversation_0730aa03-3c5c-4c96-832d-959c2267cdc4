import etcd3
from etcd3.events import PutEvent, DeleteEvent
import sys
import time

def watch_etcd_prefix(prefix, host='localhost', port=2379):
    """
    连接到 etcd 并监听指定前缀下的所有事件。

    :param prefix: 要监听的 etcd key 前缀 (例如 '/my-app/config/')
    :param host: etcd 服务的主机名
    :param port: etcd 服务的端口
    """
    try:
        # 1. 创建 etcd 客户端
        # timeout 参数是可选的，用于设置请求超时
        client = etcd3.client(host=host, port=port, timeout=5)
        print(f"成功连接到 etcd 服务 at {host}:{port}")
        
        # 确保我们能与 etcd 通信
        client.status()

    except Exception as e:
        print(f"错误: 无法连接到 etcd 服务 at {host}:{port}。")
        print(f"详细信息: {e}")
        sys.exit(1)

    print(f"[*] 开始监听 etcd 前缀: '{prefix}' ...")
    print("[*] 按 Ctrl+C 退出程序。")

    try:
        # 2. 创建一个监听器
        # watch_prefix 方法返回一个事件迭代器和一个取消函数
        events_iterator, cancel = client.watch_prefix(prefix, prev_kv=True)

        # 3. 在一个循环中处理事件
        for event in events_iterator:
            # 使用 isinstance 来判断事件类型
            if isinstance(event, PutEvent):
                # PUT 事件表示 key 被创建或更新
                key = event.key.decode('utf-8')
                value = event.value.decode('utf-8')
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [PUT] Key='{key}', Value='{value}'")
                if event.prev_value is not None:
                    prev_value = event.prev_value.decode('utf-8')
                    print(f"  [Previous Value] '{prev_value}'")
          

            elif isinstance(event, DeleteEvent):
                # DELETE 事件表示 key 被删除
                key = event.key.decode('utf-8')
                value = event.value.decode('utf-8')
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [DELETE] Key='{key}', Value='{value}'")

    except KeyboardInterrupt:
        print("\n[*] 收到用户中断信号，正在停止监听...")
        # 调用 cancel 函数来关闭监听器
        cancel()
        print("[*] 监听已停止。程序退出。")
        sys.exit(0)
    except Exception as e:
        print(f"\n[!] 在监听过程中发生错误: {e}")
        # 出现其他异常时也尝试取消监听
        if 'cancel' in locals():
            cancel()
        sys.exit(1)

def get_prefix_keys(prefix, host='localhost', port=2379):
    """
    获取指定前缀下的所有键。

    :param client: 已连接的 etcd 客户端
    :param prefix: 要查询的 etcd key 前缀
    :return: 包含所有键的列表
    """
    try:
        # 1. 创建 etcd 客户端
        # timeout 参数是可选的，用于设置请求超时
        client = etcd3.client(host=host, port=port, timeout=5)
        print(f"成功连接到 etcd 服务 at {host}:{port}")
        
        # 确保我们能与 etcd 通信
        client.status()

    except Exception as e:
        print(f"错误: 无法连接到 etcd 服务 at {host}:{port}。")
        print(f"详细信息: {e}")
        sys.exit(1)

    print(f"[*] 查询 etcd 前缀: '{prefix}' ...")

    
    keys_iterator = client.get_prefix(key_prefix=prefix, keys_only=True)

    keys = set()
    # 遍历所有匹配的键
    for key_bytes, _ in keys_iterator:
        # 直接对 key_bytes 进行解码
        key_str = key_bytes.decode('utf-8')
        print(f"Found key: {key_str}")

        # 从键字符串中移除父目录和前缀部分，得到相对路径
        # 例如: 从 '/data/sd1/key1' 变成 '1/key1'
        relative_path = key_str[len(prefix):]
        
        # 路径中可能的第一部分就是我们想要的目录后缀
        # 例如: 从 '1/key1' 得到 '1'
        if '/' in relative_path:
            subdir_suffix = relative_path.split('/')[0]
            # 完整的目录名是前缀 + 后缀
            full_subdir = f"{prefix}{subdir_suffix}"
            keys.add(full_subdir)

    return keys

if __name__ == "__main__":
    # --- 配置 ---
    ETCD_HOST = '*************'
    ETCD_PORT = 30379
    # 你想要监听的目录（前缀）。必须以 '/' 结尾来模拟目录行为。
    WATCH_PREFIX = '/network/server/test/'
    
    watch_etcd_prefix(prefix=WATCH_PREFIX, host=ETCD_HOST, port=ETCD_PORT)
    # keys = get_prefix_keys(WATCH_PREFIX, host=ETCD_HOST, port=ETCD_PORT)
    # print(f"Keys under prefix '{WATCH_PREFIX}':")