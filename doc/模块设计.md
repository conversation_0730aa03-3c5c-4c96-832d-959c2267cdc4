## api server 模块设计
### 业务逻辑

##### api服务
  提供外部访问的api接口, 接收外部请求, 调用 core service 模块的业务逻辑处理;

##### 创建子网网关
  参数：tenant_id, vlan_id, subnet_gw_ip
  调用 core service 模块的创建子网网关逻辑;

##### 创建EIP
  参数：tenant_id, vlan_id, eip, gateway_ip, internal_ip
  调用 core service 模块的创建EIP逻辑;

## core service 模块设计
### 业务逻辑

#### 初始化 etcd 连接
  启动服务时加载配置文件, 根据etcd配置连接到 etcd
server_config 配置文件
```yaml
server:
  port: 30090
  shard_count: 32
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
logging:
  level: INFO
  file: ''
```

#### 初始化分片目录
  根据 shard_count 配置, 初始化 etcd 分片目录; 比如分片数为32, 则创建 /network/server/instruct/s1~/network/server/instruct/s32

#### agent注册处理
  监听ctcd 的 /network/agent/register/ 目录, 接收到事件时执行处理逻辑;

  DELETE 事件: 修改 /network/server/metadata 数据, 对应 agent 的 online 字段设置为 false, offline_time 设置为当前时间; 
  如果 shard 字段不为空, 则将对应 agent 绑定的 shard 重新分配给其他 online 状态的 agent, 并将 shard 字段设置为空;

  PUT 事件: 根据 prev_kv 判断 status 字段变化, 如果是 running -> running, 则不处理;
  如果是 running -> error, 执行逻辑同 DELETE 事件;
  如果是 error -> running 或 null -> running, 则修改 /network/server/metadata 数据, 对应 agent 的 online 字段设置为 true, offline_time 设置为 0;

/network/server/metadata
```json
{
  "server":{
      "shard_count": 32
  },
  "agent":[
    {
      "online": true,
      "agent_id":"agent-1",
      "offline_time": 0,
      "shard":["s1"]
    },
    {
      "online": true,
      "agent_id":"agent-2",
      "offline_time": 0,
      "shard":["s2"]
    }
  ]
}
```

#### 创建子网网关
  参数: vlan_id, subnet_gw_ip
  根据指令模板生成指令, 并写入 /network/server/instruct/{shard}/{id}

  创建指令模板
  ```bash
  #创建vlan 的网络空间
  ip netns add ns-vlan{vlan_id}

  #------------------
  # 创建 vlan veth pair
  ip link add v-lan-host-{vlan_id} type veth peer name v-lan-ns-{vlan_id}
  # 将veth-ns放入命名空间
  ip link set v-lan-ns-{vlan_id} netns ns-vlan{vlan_id}
  # 配置命名空间内网络
  ip netns exec ns-vlan{vlan_id} ip addr add {subnet_gw_ip}/24 dev v-lan-ns-{vlan_id}
  ip netns exec ns-vlan{vlan_id} ip addr

  # 添加到vlan网桥并且配置vlanid
  ovs-vsctl add-port br-vlan v-lan-host-{vlan_id}  tag={vlan_id}
  #-------------- 统一启动
  ip link set v-lan-host-{vlan_id} up
  ip netns exec ns-vlan{vlan_id} ip link set v-lan-ns-{vlan_id} up
  ip netns exec ns-vlan{vlan_id} ip link set lo up
  ip link set v-lan-host-{vlan_id}  up

  ```


#### 创建EIP
  参数: tenant_id, vlan_id, eip, gateway_ip, internal_ip
  根据指令模板生成指令配置, 基于tenant_id进行取模计算, 获取对应的 shard, 并写入 /network/server/instruct/{shard}/{id}

 /network/server/instruct/{shard}/{id}
```json
{
  "type":"create_gateway",
  "tenant_id":"....",
  "request_id":"333",
  "cmds":[
    "echo 1",
    "echo 2"
  ],
  "revocation":[
    "echo u1",
    "echo u2"
  ]
}
```

  创建指令模板
```bash
  #创建对应的EIP 虚拟网卡对
  ip link add v-eip-host-{vlan_id} type veth peer name v-eip-ns-{vlan_id}
  ip link set v-eip-ns-{vlan_id} netns ns-vlan{vlan_id}

  ip netns exec ns-vlan{vlan_id} ip addr add {eip}/24 dev v-eip-ns-{vlan_id}
  #把EIP配置到eip 网桥
  ovs-vsctl add-port br-eip v-eip-host-{vlan_id}

  #启动网卡
  ip link set v-eip-host-{vlan_id} up
  ip netns exec ns-vlan{vlan_id} ip link set v-eip-ns-{vlan_id} up
  ip netns exec ns-vlan{vlan_id} ip link set lo up

  #添加默认网关
  ip netns exec ns-vlan{vlan_id} ip route add default via {gateway_ip} dev v-eip-ns-{vlan_id}
  ip netns exec ns-vlan{vlan_id} ip addr


  # ----------------
  # 添加 nat

  ip netns exec ns-vlan{vlan_id} iptables  -t nat  -L -n -v --line-numbers
  ip netns exec ns-vlan{vlan_id}  iptables -t nat -A PREROUTING -d {eip} -j DNAT --to-destination {internal_ip}

  ip netns exec ns-vlan{vlan_id}  iptables -t nat -A POSTROUTING -s {internal_ip} -j SNAT --to-source {eip}
  #删除后再通过 nft 增加
  ip netns exec ns-vlan{vlan_id}  iptables -t nat -D POSTROUTING 1
  ip netns exec ns-vlan{vlan_id}  nft add rule ip nat POSTROUTING \
      ip saddr {gateway_id} \
      ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } \
      snat to {eip}
      
  ip netns exec ns-vlan{vlan_id}  nft list table ip nat


  # 删除 
  # iptables -t nat -D POSTROUTING 1
  ip netns exec ns-vlan{vlan_id} iptables -t nat -L -n --line-numbers
  ip netns exec ns-vlan{vlan_id}  nft list table ip nat

```

## agent 模块设计

### 业务逻辑

#### 加载配置
  启动agent时加载配置文件, 根据etcd配置连接到 etcd
agent_config 配置文件
```yaml
agent:
  agent_id: agent-1
  check_env: true
  heartbeat_interval: 30.0
  max_retries: 3
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
logging:
  level: INFO
  file: ''
```

#### agent注册
  启动时, 初始化 heartbeat 线程, 根据 heartbeat_interval 配置定时执行;

  判断 check_env 配置, 如果为 true, 则执行环境检查, 如果检查不通过, 则设置 status 为 error, 检查通或check_env配置为false, 过则设置为 running;

#### 环境检查
  检查 ovs是否安装, 检查ip转发配置:`net.ipv4.ip_forward = 1`,`net.ipv4.conf.all.rp_filter = 0`,`net.ipv4.conf.default.rp_filter = 0`

  根据 heartbeat_interval 配置定时向etcd更新 key: /network/agent/register/{agent_id} , key 绑定租约, 租约时间设置为 heartbeat_interval 的 3 倍;

/network/agent/register/{agent_id} 
```json
{
  "status":"running | error",
  "agent_id": "agent-1",
  "lase_heartbeat_time": 1751526218187
}
```

#### 指令执行
##### 元数据监听
  启动时, 监听 etcd /network/server/metadata 的值, 读取当前 agent 的 shard 信息;
##### 分片同步
  当 shard 信息变化时, 检查 /network/agent/task/{agent_id} 目录, 如果分片不一致, 则根据 instruct.revocation 执行撤销命令;
##### 任务执行
  根据获取的分片列表, 监听etcd /network/server/instruct/{shard} 目录, 接收到 DELETE事件: 读取revocation字段, 获取命令列表执行撤销命令; 接收到 PUT 事件时判断是新增还是更新, 新增则读取cmds字段获取命令列表安顺序执行, 更新则先执行撤销命令, 在执行新增命令; 
  每次执行命令前, 先在 /network/agent/task/{agent_id}/{shard}/{id} 创建任务数据, status 设置为 exec, 执行命令结束后, 如果执行成功, 则设置为 ok, 失败则设置为 err;


/network/agent/task/{agent_id}/{shard}/{id}
```json
{
  "instruct":{
    "type":"create_gateway",
    "request_id":"333",
    "cmds":[
      "echo 1",
      "echo 2"
    ],
    "revocation":[
      "echo u1",
      "echo u2"
    ]
  },
  "status":"exec | ok | err",
  "message":""
}
```