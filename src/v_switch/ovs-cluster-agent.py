#!/usr/bin/env python3
import time
import json
import subprocess
import etcd3
import argparse
import logging

class OVSClusterAgent:
    def __init__(self, node_id, etcd_host='localhost', etcd_port=2379):
        self.node_id = node_id
        self.etcd = etcd3.client(host=etcd_host, port=etcd_port)
        self.logger = logging.getLogger(__name__)
        
    def register_node(self):
        """注册节点信息"""
        node_info = {
            'node_id': self.node_id,
            'ip': self.get_local_ip(),
            'status': 'active',
            'timestamp': int(time.time())
        }
        
        
        key = f'/ovs-cluster/nodes/{self.node_id}'
        self.etcd.put(key, json.dumps(node_info), lease=30)
        
    def get_local_ip(self):
        """获取本地 IP"""
        result = subprocess.run(['hostname', '-I'], 
                              capture_output=True, text=True)
        return result.stdout.strip().split()[0]
        
    def watch_tenant_changes(self):
        """监听租户网络变化"""
        events_iterator, cancel = self.etcd.watch_prefix('/ovs-cluster/tenants/')
        
        for event in events_iterator:
            if event.type == 'PUT':
                self.handle_tenant_create(event.key, event.value)
            elif event.type == 'DELETE':
                self.handle_tenant_delete(event.key)
                
    def handle_tenant_create(self, key, value):
        """处理租户网络创建"""
        tenant_data = json.loads(value)
        tenant_id = tenant_data['tenant_id']
        
        # 根据负载均衡策略决定是否在本节点创建
        if self.should_handle_tenant(tenant_id):
            self.create_local_tenant_network(tenant_data)
            
    def should_handle_tenant(self, tenant_id):
        """负载均衡策略：奇数租户在节点1，偶数在节点2"""
        tenant_num = int(tenant_id.replace('tenant', ''))
        return (tenant_num % 2) == (self.node_id - 1)
        
    def create_local_tenant_network(self, tenant_data):
        """在本地创建租户网络"""
        tenant_id = tenant_data['tenant_id']
        vlan_id = tenant_data['vlan_id']
        subnet = tenant_data['subnet']
        
        # 执行网络创建命令
        commands = [
            f"ip netns add {tenant_id}",
            f"ip link add veth-{tenant_id}-host type veth peer name veth-{tenant_id}-ns",
            f"ip link set veth-{tenant_id}-ns netns {tenant_id}",
            f"ip netns exec {tenant_id} ip addr add {subnet} dev veth-{tenant_id}-ns",
            f"ip netns exec {tenant_id} ip link set veth-{tenant_id}-ns up",
            f"ip netns exec {tenant_id} ip link set lo up",
            f"ovs-vsctl add-port br-int veth-{tenant_id}-host tag={vlan_id}",
            f"ip link set veth-{tenant_id}-host up"
        ]
        
        for cmd in commands:
            subprocess.run(cmd.split(), check=True)
            
    def run(self):
        """主运行循环"""
        while True:
            try:
                self.register_node()
                self.watch_tenant_changes()
            except Exception as e:
                self.logger.error(f"Error in cluster agent: {e}")
                time.sleep(5)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--node-id', type=int, required=True)
    args = parser.parse_args()
    
    agent = OVSClusterAgent(args.node_id)
    agent.run()