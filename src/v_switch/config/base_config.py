"""
Base configuration class for v-switch components.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class ETCDConfig:
    """ETCD connection configuration."""
    host: str = "localhost"
    port: int = 2379
    timeout: float = 5.0
    username: str = ""
    password: str = ""


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    file: str = ""
    console: bool = True


class BaseConfig:
    """Base configuration class with YAML loading support."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration.
        
        Args:
            config_file: Path to YAML configuration file
        """
        self.etcd = ETCDConfig()
        self.logging = LoggingConfig()
        
        if config_file:
            self.load_from_file(config_file)

    @classmethod
    def from_file(cls, config_file: str):
        """Create configuration instance from YAML file.

        Args:
            config_file: Path to YAML configuration file

        Returns:
            Configuration instance

        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
        """
        instance = cls()
        instance.load_from_file(config_file)
        return instance

    def load_from_file(self, config_file: str) -> None:
        """Load configuration from YAML file.
        
        Args:
            config_file: Path to YAML configuration file
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
        """
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if config_data:
                self._update_from_dict(config_data)
                
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Invalid YAML in config file {config_file}: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary
        """
        # Update ETCD config
        if 'etcd' in config_data:
            etcd_config = config_data['etcd']
            for key, value in etcd_config.items():
                if hasattr(self.etcd, key):
                    setattr(self.etcd, key, value)
        
        # Update logging config
        if 'logging' in config_data:
            logging_config = config_data['logging']
            for key, value in logging_config.items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        return {
            'etcd': {
                'host': self.etcd.host,
                'port': self.etcd.port,
                'timeout': self.etcd.timeout,
                'username': self.etcd.username,
                'password': self.etcd.password,
            },
            'logging': {
                'level': self.logging.level,
                'file': self.logging.file,
            }
        }
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration."""
        log_level = getattr(logging, self.logging.level.upper(), logging.INFO)
        
        # Configure logging format
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        if self.logging.file:
            # Log to file
            logging.basicConfig(
                level=log_level,
                format=log_format,
                filename=self.logging.file,
                filemode='a'
            )
        else:
            # Log to console
            logging.basicConfig(
                level=log_level,
                format=log_format
            )
