"""
Server configuration for v-switch core service.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from .base_config import BaseConfig


@dataclass
class ServerSettings:
    """Server-specific settings."""
    port: int = 30090
    shard_count: int = 32


class ServerConfig(BaseConfig):
    """Configuration for v-switch core service server."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize server configuration.
        
        Args:
            config_file: Path to YAML configuration file
        """
        super().__init__(config_file)
        self.server = ServerSettings()
        
        if config_file:
            self.load_from_file(config_file)
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary
        """
        # Call parent method for common configs
        super()._update_from_dict(config_data)
        
        # Update server-specific config
        if 'server' in config_data:
            server_config = config_data['server']
            for key, value in server_config.items():
                if hasattr(self.server, key):
                    setattr(self.server, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        config_dict = super().to_dict()
        config_dict['server'] = {
            'port': self.server.port,
            'shard_count': self.server.shard_count,
        }
        return config_dict
    
    def get_shard_paths(self) -> list[str]:
        """Get list of shard paths for ETCD.
        
        Returns:
            List of shard paths like ['/network/server/instruct/s1', ...]
        """
        return [f"/network/server/instruct/s{i+1}" 
                for i in range(self.server.shard_count)]
    
    def get_shard_for_tenant(self, tenant_id: str) -> str:
        """Get shard path for a tenant based on hash.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Shard path for the tenant
        """
        # Simple hash-based sharding
        shard_index = hash(tenant_id) % self.server.shard_count
        return f"s{shard_index + 1}"
