"""
Heartbeat manager for v-switch agent.
"""

import json
import time
import threading
import logging
from typing import Optional, Dict, Any
from ..common.etcd_client import ETCDClient
from ..config.agent_config import AgentConfig
from .environment_checker import EnvironmentChecker


class HeartbeatManager:
    """Manages agent heartbeat and registration with ETCD."""
    
    def __init__(self, etcd_client: ETCDClient, config: AgentConfig):
        """
        初始化心跳管理器

        Args:
            etcd_client: ETCD client instance
            config: Agent configuration
        """
        self.etcd_client = etcd_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.env_checker = EnvironmentChecker()
        self._heartbeat_thread = None
        self._running = False
        self._lease_id = None
        self._current_status = "unknown"
    
    def start(self) -> bool:
        """
        启动心跳管理器
        
        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("Heartbeat manager is already running")
                return True
            
            # 判断是否需要检查环境
            if self.config.agent.check_env:
                env_ok, env_errors = self.env_checker.check_all()
                if not env_ok:
                    self._current_status = "error"
                    self.logger.error(f"Environment check failed: {env_errors}")
                    # 继续以错误状态注册
                else:
                    self._current_status = "running"
                    self.logger.info("Environment check passed")
            else:
                self._current_status = "running"
                self.logger.info("Environment check disabled")
            
            # 创建初始租约
            # ttl = self.config.get_lease_ttl()
            # self._lease = self.etcd_client.create_lease(ttl)
            # if not self._lease:
            #     self.logger.error("Failed to create initial lease")
            #     return False
            # else:
            #     self._lease.keepalive_forever()
            
            # 执行初始注册
            # if not self._register():
            #     self.logger.error("Failed to perform initial registration")
            #     return False

            # 启动心跳线程
            self._running = True
            self._heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop,
                name="HeartbeatManager",
                daemon=True
            )
            self._heartbeat_thread.start()
            
            self.logger.info("Heartbeat manager started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start heartbeat manager: {e}")
            return False
    
    def stop(self) -> None:
        """停止心跳管理器"""
        try:
            self.logger.info("Stopping heartbeat manager...")
            self._running = False
            
            # Wait for heartbeat thread to finish
            if self._heartbeat_thread and self._heartbeat_thread.is_alive():
                self._heartbeat_thread.join(timeout=5.0)
            
            # Clean up registration
            if self._lease_id:
                self.etcd_client.revoke_lease(self._lease_id)
            self._unregister()
            
            self.logger.info("Heartbeat manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping heartbeat manager: {e}")
    
    def _heartbeat_loop(self) -> None:
        """主要心跳循环"""

        if not self._lease_id:
            # 创建初始租约
            ttl = self.config.get_lease_ttl()
            self._lease_id = self.etcd_client.create_lease(ttl)
            if not self._lease_id:
                self.logger.error("Failed to create initial lease")
                return

        while self._running:
            try:
                # 刷新租约
                self.etcd_client.refresh_lease(self._lease_id)

                # 更新注册信息
                if not self._register():
                    self.logger.error("Failed to update registration")
                
                # 休眠直到下一次心跳
                time.sleep(self.config.agent.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")
                time.sleep(self.config.agent.heartbeat_interval)
    
    
    def _register(self) -> bool:
        """注册agent到ETCD
        
        Returns:
            True if successful
        """
        try:
            registration_data = {
                "status": self._current_status,
                "agent_id": self.config.agent.agent_id,
                "last_heartbeat_time": int(time.time() * 1000)  # milliseconds
            }
            
            key = self.config.get_register_key()
            success = self.etcd_client.put_json(key, registration_data, lease=self._lease_id)
            
            if success:
                self.logger.info(f"Registered agent {self.config.agent.agent_id} with status {self._current_status}")
            else:
                self.logger.error(f"Failed to register agent {self.config.agent.agent_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error registering agent: {e}")
            return False
    
    def _unregister(self) -> bool:
        """从ETCD注销agent
        
        Returns:
            True if successful
        """
        try:
            key = self.config.get_register_key()
            success = self.etcd_client.delete(key)
            
            if success:
                self.logger.info(f"Unregistered agent {self.config.agent.agent_id}")
            else:
                self.logger.error(f"Failed to unregister agent {self.config.agent.agent_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error unregistering agent: {e}")
            return False
    
    def set_status(self, status: str) -> None:
        """设置agent状态
        
        Args:
            status: New status ("running" or "error")
        """
        if status in ["running", "error"]:
            old_status = self._current_status
            self._current_status = status
            self.logger.info(f"Agent status changed from {old_status} to {status}")
        else:
            self.logger.warning(f"Invalid status: {status}")
    
    def get_status(self) -> str:
        """获取当前agent的状态

        Returns:
            Current status
        """
        return self._current_status
    
    def is_running(self) -> bool:
        """检查心跳管理器是否正在运行
        
        Returns:
            True if running
        """
        return self._running
    
    def get_registration_info(self) -> Optional[Dict[str, Any]]:
        """获取当前注册信息
        
        Returns:
            Registration data or None if not found
        """
        try:
            key = self.config.get_register_key()
            data, _ = self.etcd_client.get_json(key)
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting registration info: {e}")
            return None
