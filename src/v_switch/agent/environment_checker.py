"""
Environment checker for v-switch agent.
"""

import os
import subprocess
import logging
from typing import List, Tu<PERSON>, Dict, Any


class EnvironmentChecker:
    """Checks system environment for v-switch agent requirements."""
    
    def __init__(self):
        """Initialize environment checker."""
        self.logger = logging.getLogger(__name__)
    
    def check_all(self) -> Tuple[bool, List[str]]:
        """
        检查所有环境要求
        
        Returns:
            Tuple of (success, error_messages)
        """
        errors = []
        
        # Check OVS installation
        if not self.check_ovs():
            errors.append("Open vSwitch (OVS) is not installed or not accessible")
        
        # Check IP forwarding
        if not self.check_ip_forwarding():
            errors.append("IP forwarding is not enabled (net.ipv4.ip_forward = 1)")
        
        # Check RP filter settings
        rp_filter_errors = self.check_rp_filter()
        if rp_filter_errors:
            errors.extend(rp_filter_errors)
        
        # Check required commands
        missing_commands = self.check_required_commands()
        if missing_commands:
            errors.append(f"Missing required commands: {', '.join(missing_commands)}")
        
        success = len(errors) == 0
        return success, errors
    
    def check_ovs(self) -> bool:
        """
        检查OVS是否安装
        
        Returns:
            True if OVS is available
        """
        try:
            # Try to run ovs-vsctl version
            result = subprocess.run(
                ['ovs-vsctl', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.logger.debug("OVS check passed")
                return True
            else:
                self.logger.error(f"OVS check failed: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            self.logger.error(f"OVS check failed: {e}")
            return False
    
    def check_ip_forwarding(self) -> bool:
        """
        检查IP转发是否开启
        
        Returns:
            True if IP forwarding is enabled
        """
        try:
            with open('/proc/sys/net/ipv4/ip_forward', 'r') as f:
                value = f.read().strip()
            
            if value == '1':
                self.logger.debug("IP forwarding check passed")
                return True
            else:
                self.logger.error(f"IP forwarding is disabled (value: {value})")
                return False
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            self.logger.error(f"IP forwarding check failed: {e}")
            return False
    
    def check_rp_filter(self) -> List[str]:
        """
        检查RP过滤器设置
        
        Returns:
            List of error messages (empty if all checks pass)
        """
        errors = []
        
        # Check net.ipv4.conf.all.rp_filter = 0
        try:
            with open('/proc/sys/net/ipv4/conf/all/rp_filter', 'r') as f:
                value = f.read().strip()
            
            if value != '0':
                errors.append(f"net.ipv4.conf.all.rp_filter should be 0 (current: {value})")
            else:
                self.logger.debug("RP filter all check passed")
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            errors.append(f"Failed to check net.ipv4.conf.all.rp_filter: {e}")
        
        # Check net.ipv4.conf.default.rp_filter = 0
        try:
            with open('/proc/sys/net/ipv4/conf/default/rp_filter', 'r') as f:
                value = f.read().strip()
            
            if value != '0':
                errors.append(f"net.ipv4.conf.default.rp_filter should be 0 (current: {value})")
            else:
                self.logger.debug("RP filter default check passed")
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            errors.append(f"Failed to check net.ipv4.conf.default.rp_filter: {e}")
        
        return errors
    
    def check_required_commands(self) -> List[str]:
        """
        检查所有需要的命令是否可用
        
        Returns:
            List of missing commands
        """
        required_commands = [
            'ip',
            'iptables',
            'nft',
            'ovs-vsctl'
        ]
        
        missing = []
        
        for cmd in required_commands:
            try:
                result = subprocess.run(
                    ['which', cmd],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if result.returncode != 0:
                    missing.append(cmd)
                else:
                    self.logger.debug(f"Command {cmd} found")
                    
            except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                self.logger.error(f"Failed to check command {cmd}: {e}")
                missing.append(cmd)
        
        return missing
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            Dictionary with system information
        """
        info = {}
        
        # Get kernel version
        try:
            with open('/proc/version', 'r') as f:
                info['kernel_version'] = f.read().strip()
        except Exception:
            info['kernel_version'] = "Unknown"
        
        # Get OS release
        try:
            with open('/etc/os-release', 'r') as f:
                os_release = {}
                for line in f:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        os_release[key] = value.strip('"')
                info['os_release'] = os_release
        except Exception:
            info['os_release'] = {}
        
        # Get network interfaces
        try:
            result = subprocess.run(
                ['ip', 'link', 'show'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                info['network_interfaces'] = result.stdout
        except Exception:
            info['network_interfaces'] = "Failed to get network interfaces"
        
        return info
