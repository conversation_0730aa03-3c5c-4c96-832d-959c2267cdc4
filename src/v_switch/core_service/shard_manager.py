"""
Shard management for v-switch core service.
"""

import logging
from typing import List, Dict, Any, Optional
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig


class ShardManager:
    """Manages ETCD shards for distributing workload."""
    
    def __init__(self, etcd_client: ETCDClient, config: ServerConfig):
        """Initialize shard manager.
        
        Args:
            etcd_client: ETCD client instance
            config: Server configuration
        """
        self.etcd_client = etcd_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def initialize_shards(self) -> bool:
        """Initialize shard directories in ETCD.
        
        Returns:
            True if successful
        """
        try:
            shard_paths = self.config.get_shard_paths()
            
            for shard_path in shard_paths:
                # Ensure shard directory exists
                if not self.etcd_client.ensure_directory(shard_path):
                    self.logger.error(f"Failed to create shard directory: {shard_path}")
                    return False
                    
            self.logger.info(f"Initialized {len(shard_paths)} shard directories")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize shards: {e}")
            return False
    
    def get_shard_for_tenant(self, tenant_id: str) -> str:
        """Get shard identifier for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Shard identifier (e.g., 's1', 's2', ...)
        """
        return self.config.get_shard_for_tenant(tenant_id)
    
    def get_shard_path(self, shard_id: str) -> str:
        """Get full ETCD path for a shard.
        
        Args:
            shard_id: Shard identifier (e.g., 's1')
            
        Returns:
            Full ETCD path for the shard
        """
        return f"/network/server/instruct/{shard_id}"
    
    def get_instruction_key(self, shard_id: str, instruction_id: str) -> str:
        """Get ETCD key for an instruction.
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            Full ETCD key for the instruction
        """
        return f"{self.get_shard_path(shard_id)}/{instruction_id}"
    
    def put_instruction(self, shard_id: str, instruction_id: str, 
                       instruction_data: Dict[str, Any]) -> bool:
        """Put an instruction to a shard.
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            instruction_data: Instruction data
            
        Returns:
            True if successful
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            success = self.etcd_client.put_json(key, instruction_data)
            
            if success:
                self.logger.info(f"Put instruction {instruction_id} to shard {shard_id}")
            else:
                self.logger.error(f"Failed to put instruction {instruction_id} to shard {shard_id}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error putting instruction {instruction_id} to shard {shard_id}: {e}")
            return False
    
    def get_instruction(self, shard_id: str, instruction_id: str) -> Optional[Dict[str, Any]]:
        """Get an instruction from a shard.
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            Instruction data or None if not found
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            data, _ = self.etcd_client.get_json(key)
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting instruction {instruction_id} from shard {shard_id}: {e}")
            return None
    
    def delete_instruction(self, shard_id: str, instruction_id: str) -> bool:
        """Delete an instruction from a shard.
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            True if successful
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            success = self.etcd_client.delete(key)
            
            if success:
                self.logger.info(f"Deleted instruction {instruction_id} from shard {shard_id}")
            else:
                self.logger.error(f"Failed to delete instruction {instruction_id} from shard {shard_id}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error deleting instruction {instruction_id} from shard {shard_id}: {e}")
            return False
    
    def list_shard_instructions(self, shard_id: str) -> List[str]:
        """List all instruction IDs in a shard.
        
        Args:
            shard_id: Shard identifier
            
        Returns:
            List of instruction IDs
        """
        try:
            shard_path = self.get_shard_path(shard_id)
            instruction_ids = []
            
            for key, _ in self.etcd_client.get_prefix(shard_path):
                # Extract instruction ID from key
                if key.startswith(shard_path + "/"):
                    instruction_id = key[len(shard_path) + 1:]
                    instruction_ids.append(instruction_id)
            
            return instruction_ids
            
        except Exception as e:
            self.logger.error(f"Error listing instructions in shard {shard_id}: {e}")
            return []
