"""
Command templates for v-switch network operations.
"""

from typing import List, Dict, Any


class CommandTemplates:
    """Templates for generating network configuration commands."""
    
    @staticmethod
    def get_subnet_gateway_commands(vlan_id: str, subnet_gw_ip: str) -> List[str]:
        """Get commands for creating subnet gateway.
        
        Args:
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP
            
        Returns:
            List of commands
        """
        return [
            f"ip link add v-lan-host-{vlan_id} type veth peer name v-lan-ns-{vlan_id}",
            f"ip link set v-lan-ns-{vlan_id} netns ns-vlan{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr add {subnet_gw_ip}/24 dev v-lan-ns-{vlan_id}",
            f"ovs-vsctl add-port br-vlan v-lan-host-{vlan_id} tag={vlan_id}",
            f"ip link set v-lan-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-lan-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up"
        ]
    
    @staticmethod
    def get_subnet_gateway_revocation(vlan_id: str) -> List[str]:
        """Get revocation commands for subnet gateway.
        
        Args:
            vlan_id: VLAN identifier
            
        Returns:
            List of revocation commands
        """
        return [
            f"ovs-vsctl del-port br-vlan v-lan-host-{vlan_id}",
            f"ip link delete v-lan-host-{vlan_id}",
            f"ip netns delete ns-vlan{vlan_id}"
        ]
    
    @staticmethod
    def get_eip_commands(vlan_id: str, eip: str, gateway_ip: str, internal_ip: str) -> List[str]:
        """Get commands for creating EIP.
        
        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address
            
        Returns:
            List of commands
        """
        return [
            f"ip link add v-eip-host-{vlan_id} type veth peer name v-eip-ns-{vlan_id}",
            f"ip link set v-eip-ns-{vlan_id} netns ns-vlan{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr add {eip}/24 dev v-eip-ns-{vlan_id}",
            f"ovs-vsctl add-port br-eip v-eip-host-{vlan_id}",
            f"ip link set v-eip-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-eip-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            f"ip netns exec ns-vlan{vlan_id} ip route add default via {gateway_ip} dev v-eip-ns-{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -L -n -v --line-numbers",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -A PREROUTING -d {eip} -j DNAT --to-destination {internal_ip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -A POSTROUTING -s {internal_ip} -j SNAT --to-source {eip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D POSTROUTING 1",
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat POSTROUTING ip saddr {gateway_ip} ip daddr != {{ 10.0.0.0/8, **********/12, ***********/24,***********/24 }} snat to {eip}",
            f"ip netns exec ns-vlan{vlan_id} nft list table ip nat"
        ]
    
    @staticmethod
    def get_eip_revocation(vlan_id: str, eip: str, internal_ip: str) -> List[str]:
        """Get revocation commands for EIP.
        
        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address
            
        Returns:
            List of revocation commands
        """
        return [
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D PREROUTING -d {eip} -j DNAT --to-destination {internal_ip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D POSTROUTING -s {internal_ip} -j SNAT --to-source {eip}",
            f"ip netns exec ns-vlan{vlan_id} nft delete rule ip nat POSTROUTING handle $(ip netns exec ns-vlan{vlan_id} nft -a list table ip nat | grep 'snat to {eip}' | awk '{{print $NF}}')",
            f"ovs-vsctl del-port br-eip v-eip-host-{vlan_id}",
            f"ip link delete v-eip-host-{vlan_id}"
        ]
    
    @staticmethod
    def get_environment_check_commands() -> List[str]:
        """Get commands for environment checking.
        
        Returns:
            List of check commands
        """
        return [
            "ovs-vsctl --version",
            "cat /proc/sys/net/ipv4/ip_forward",
            "cat /proc/sys/net/ipv4/conf/all/rp_filter",
            "cat /proc/sys/net/ipv4/conf/default/rp_filter",
            "which ip",
            "which iptables",
            "which nft",
            "which ovs-vsctl"
        ]
    
    @staticmethod
    def get_system_info_commands() -> List[str]:
        """Get commands for system information gathering.
        
        Returns:
            List of info commands
        """
        return [
            "uname -a",
            "cat /etc/os-release",
            "ip link show",
            "ovs-vsctl show",
            "ip netns list"
        ]
    
    @staticmethod
    def validate_template_parameters(template_type: str, parameters: Dict[str, Any]) -> List[str]:
        """Validate template parameters.
        
        Args:
            template_type: Type of template
            parameters: Template parameters
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if template_type == "subnet_gateway":
            required = ["vlan_id", "subnet_gw_ip"]
            for param in required:
                if param not in parameters or not parameters[param]:
                    errors.append(f"Missing required parameter: {param}")
        
        elif template_type == "eip":
            required = ["vlan_id", "eip", "gateway_ip", "internal_ip"]
            for param in required:
                if param not in parameters or not parameters[param]:
                    errors.append(f"Missing required parameter: {param}")
        
        else:
            errors.append(f"Unknown template type: {template_type}")
        
        return errors
    
    @staticmethod
    def generate_commands(template_type: str, parameters: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate commands based on template type and parameters.
        
        Args:
            template_type: Type of template
            parameters: Template parameters
            
        Returns:
            Dictionary with 'commands' and 'revocation' lists
        """
        # Validate parameters
        errors = CommandTemplates.validate_template_parameters(template_type, parameters)
        if errors:
            raise ValueError(f"Template validation failed: {errors}")
        
        if template_type == "subnet_gateway":
            return {
                "commands": CommandTemplates.get_subnet_gateway_commands(
                    parameters["vlan_id"],
                    parameters["subnet_gw_ip"]
                ),
                "revocation": CommandTemplates.get_subnet_gateway_revocation(
                    parameters["vlan_id"]
                )
            }
        
        elif template_type == "eip":
            return {
                "commands": CommandTemplates.get_eip_commands(
                    parameters["vlan_id"],
                    parameters["eip"],
                    parameters["gateway_ip"],
                    parameters["internal_ip"]
                ),
                "revocation": CommandTemplates.get_eip_revocation(
                    parameters["vlan_id"],
                    parameters["eip"],
                    parameters["internal_ip"]
                )
            }
        
        else:
            raise ValueError(f"Unknown template type: {template_type}")
    
    @staticmethod
    def get_available_templates() -> List[str]:
        """Get list of available template types.
        
        Returns:
            List of template type names
        """
        return ["subnet_gateway", "eip"]
