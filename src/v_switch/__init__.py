"""
V-Switch: Distributed Network Switching System

A distributed network switching system with ETCD-based coordination,
shard management, agent registration, and network configuration capabilities.
"""

__version__ = "1.0.0"
__author__ = "V-Switch Team"
__description__ = "Distributed Network Switching System"

# Core components
from .config import BaseConfig, ServerConfig, AgentConfig
from .common import ETCDClient
from .core_service import CoreService
from .agent import VSwitchAgent
from .api_server import APIServer
from .templates import CommandTemplates
from .utils import setup_logging, ValidationUtils

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__description__",

    # Configuration
    "BaseConfig",
    "ServerConfig",
    "AgentConfig",

    # Core components
    "ETCDClient",
    "CoreService",
    "VSwitchAgent",
    "APIServer",

    # Templates and utilities
    "CommandTemplates",
    "setup_logging",
    "ValidationUtils",
]

# Import main components
from .config import ServerConfig, AgentConfig
from .common.etcd_client import ETCDClient

__all__ = [
    "ServerConfig",
    "AgentConfig",
    "ETCDClient",
]
