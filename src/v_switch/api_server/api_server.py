"""
API server for v-switch.
"""

import logging
import threading
from http.server import <PERSON><PERSON>PServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any, Optional
from ..config.server_config import ServerConfig
from ..core_service.core_service import CoreService
from .handlers import <PERSON><PERSON><PERSON><PERSON>, StatusHandler


class VSwitchHTTPRequestHandler(BaseHTTPRequestHandler):
    """HTTP request handler for v-switch API."""
    
    def __init__(self, network_handler: NetworkHandler, status_handler: StatusHandler, *args, **kwargs):
        """Initialize request handler.
        
        Args:
            network_handler: Network handler instance
            status_handler: Status handler instance
        """
        self.network_handler = network_handler
        self.status_handler = status_handler
        self.logger = logging.getLogger(__name__)
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """Override log message to use our logger."""
        self.logger.info(format % args)
    
    def do_GET(self) -> None:
        """Handle GET requests."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            self.logger.info(f"GET {path}")
            
            if path == "/health":
                self.status_handler.handle_health_check(self)
            elif path == "/status":
                self.status_handler.handle_server_status(self)
            elif path.startswith("/instruction/"):
                self._handle_get_instruction(path, query_params)
            else:
                self._send_not_found()
                
        except Exception as e:
            self.logger.error(f"Error handling GET request: {e}")
            self._send_internal_error(str(e))
    
    def do_POST(self) -> None:
        """Handle POST requests."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            self.logger.info(f"POST {path}")
            
            if path == "/network/subnet-gateway":
                self.network_handler.handle_create_subnet_gateway(self)
            elif path == "/network/eip":
                self.network_handler.handle_create_eip(self)
            else:
                self._send_not_found()
                
        except Exception as e:
            self.logger.error(f"Error handling POST request: {e}")
            self._send_internal_error(str(e))
    
    def do_DELETE(self) -> None:
        """Handle DELETE requests."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            self.logger.info(f"DELETE {path}")
            
            if path.startswith("/instruction/"):
                self._handle_delete_instruction(path, query_params)
            else:
                self._send_not_found()
                
        except Exception as e:
            self.logger.error(f"Error handling DELETE request: {e}")
            self._send_internal_error(str(e))
    
    def _handle_get_instruction(self, path: str, query_params: Dict[str, list]) -> None:
        """Handle GET instruction request.
        
        Args:
            path: Request path
            query_params: Query parameters
        """
        # Extract request_id from path: /instruction/{request_id}
        path_parts = path.strip('/').split('/')
        if len(path_parts) != 2 or path_parts[0] != 'instruction':
            self._send_bad_request("Invalid instruction path")
            return
        
        request_id = path_parts[1]
        
        # Get tenant_id from query parameters
        tenant_ids = query_params.get('tenant_id', [])
        if not tenant_ids:
            self._send_bad_request("Missing tenant_id query parameter")
            return
        
        tenant_id = tenant_ids[0]
        
        self.network_handler.handle_get_instruction_status(self, request_id, tenant_id)
    
    def _handle_delete_instruction(self, path: str, query_params: Dict[str, list]) -> None:
        """Handle DELETE instruction request.
        
        Args:
            path: Request path
            query_params: Query parameters
        """
        # Extract request_id from path: /instruction/{request_id}
        path_parts = path.strip('/').split('/')
        if len(path_parts) != 2 or path_parts[0] != 'instruction':
            self._send_bad_request("Invalid instruction path")
            return
        
        request_id = path_parts[1]
        
        # Get tenant_id from query parameters
        tenant_ids = query_params.get('tenant_id', [])
        if not tenant_ids:
            self._send_bad_request("Missing tenant_id query parameter")
            return
        
        tenant_id = tenant_ids[0]
        
        self.network_handler.handle_delete_instruction(self, request_id, tenant_id)
    
    def _send_not_found(self) -> None:
        """Send 404 Not Found response."""
        self.send_response(404)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response = '{"error": true, "message": "Not Found", "status_code": 404}'
        self.wfile.write(response.encode('utf-8'))
    
    def _send_bad_request(self, message: str) -> None:
        """Send 400 Bad Request response.
        
        Args:
            message: Error message
        """
        self.send_response(400)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response = f'{{"error": true, "message": "{message}", "status_code": 400}}'
        self.wfile.write(response.encode('utf-8'))
    
    def _send_internal_error(self, message: str) -> None:
        """Send 500 Internal Server Error response.
        
        Args:
            message: Error message
        """
        self.send_response(500)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response = f'{{"error": true, "message": "Internal Server Error: {message}", "status_code": 500}}'
        self.wfile.write(response.encode('utf-8'))


class APIServer:
    """Main API server class."""
    
    def __init__(self, config: ServerConfig, core_service: CoreService):
        """Initialize API server.
        
        Args:
            config: Server configuration
            core_service: Core service instance
        """
        self.config = config
        self.core_service = core_service
        self.logger = logging.getLogger(__name__)
        
        # Initialize handlers
        self.network_handler = NetworkHandler(core_service)
        self.status_handler = StatusHandler(core_service)
        
        # Server state
        self.httpd = None
        self.server_thread = None
        self._running = False
    
    def start(self) -> bool:
        """Start the API server.
        
        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("API server is already running")
                return True
            
            # Create HTTP server
            server_address = ('', self.config.server.port)
            
            # Create handler class with our handlers
            def handler_factory(*args, **kwargs):
                return VSwitchHTTPRequestHandler(
                    self.network_handler, 
                    self.status_handler, 
                    *args, **kwargs
                )
            
            self.httpd = HTTPServer(server_address, handler_factory)
            
            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                name="APIServer",
                daemon=True
            )
            
            self._running = True
            self.server_thread.start()
            
            self.logger.info(f"API server started on port {self.config.server.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the API server."""
        try:
            if not self._running:
                return
            
            self.logger.info("Stopping API server...")
            self._running = False
            
            # Shutdown HTTP server
            if self.httpd:
                self.httpd.shutdown()
                self.httpd.server_close()
            
            # Wait for server thread to finish
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)
            
            self.logger.info("API server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping API server: {e}")
    
    def _run_server(self) -> None:
        """Run the HTTP server."""
        try:
            self.logger.info("Starting HTTP server loop")
            self.httpd.serve_forever()
        except Exception as e:
            if self._running:  # Only log if we're supposed to be running
                self.logger.error(f"HTTP server error: {e}")
    
    def is_running(self) -> bool:
        """Check if API server is running.
        
        Returns:
            True if running
        """
        return self._running
