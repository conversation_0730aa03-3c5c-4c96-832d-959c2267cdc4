"""
Request handlers for v-switch API server.
"""

import json
import logging
from http.server import BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any, Optional
from ..core_service.core_service import CoreService


class BaseHandler:
    """Base handler for API requests."""
    
    def __init__(self, core_service: CoreService):
        """Initialize handler.
        
        Args:
            core_service: Core service instance
        """
        self.core_service = core_service
        self.logger = logging.getLogger(__name__)
    
    def send_json_response(self, handler: BaseHTTPRequestHandler, status_code: int, 
                          data: Dict[str, Any]) -> None:
        """Send JSON response.
        
        Args:
            handler: HTTP request handler
            status_code: HTTP status code
            data: Response data
        """
        try:
            response_body = json.dumps(data, indent=2)
            
            handler.send_response(status_code)
            handler.send_header('Content-Type', 'application/json')
            handler.send_header('Content-Length', str(len(response_body)))
            handler.end_headers()
            handler.wfile.write(response_body.encode('utf-8'))
            
        except Exception as e:
            self.logger.error(f"Error sending JSON response: {e}")
    
    def send_error_response(self, handler: BaseHTTPRequestHandler, status_code: int, 
                           message: str) -> None:
        """Send error response.
        
        Args:
            handler: HTTP request handler
            status_code: HTTP status code
            message: Error message
        """
        error_data = {
            "error": True,
            "message": message,
            "status_code": status_code
        }
        self.send_json_response(handler, status_code, error_data)
    
    def parse_json_body(self, handler: BaseHTTPRequestHandler) -> Optional[Dict[str, Any]]:
        """Parse JSON request body.
        
        Args:
            handler: HTTP request handler
            
        Returns:
            Parsed JSON data or None if invalid
        """
        try:
            content_length = int(handler.headers.get('Content-Length', 0))
            if content_length == 0:
                return {}
            
            body = handler.rfile.read(content_length)
            return json.loads(body.decode('utf-8'))
            
        except Exception as e:
            self.logger.error(f"Error parsing JSON body: {e}")
            return None


class NetworkHandler(BaseHandler):
    """Handler for network-related API endpoints."""
    
    def handle_create_subnet_gateway(self, handler: BaseHTTPRequestHandler) -> None:
        """Handle create subnet gateway request.
        
        Args:
            handler: HTTP request handler
        """
        try:
            # Parse request body
            data = self.parse_json_body(handler)
            if data is None:
                self.send_error_response(handler, 400, "Invalid JSON in request body")
                return
            
            # Validate required fields
            required_fields = ['tenant_id', 'vlan_id', 'subnet_gw_ip']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                self.send_error_response(
                    handler, 400, 
                    f"Missing required fields: {', '.join(missing_fields)}"
                )
                return
            
            # Extract parameters
            tenant_id = data['tenant_id']
            vlan_id = data['vlan_id']
            subnet_gw_ip = data['subnet_gw_ip']
            
            # Call core service
            request_id = self.core_service.create_subnet_gateway(tenant_id, vlan_id, subnet_gw_ip)
            
            if request_id:
                response_data = {
                    "success": True,
                    "request_id": request_id,
                    "message": "Subnet gateway creation request submitted"
                }
                self.send_json_response(handler, 200, response_data)
            else:
                self.send_error_response(handler, 500, "Failed to create subnet gateway")
                
        except Exception as e:
            self.logger.error(f"Error handling create subnet gateway: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")
    
    def handle_create_eip(self, handler: BaseHTTPRequestHandler) -> None:
        """Handle create EIP request.
        
        Args:
            handler: HTTP request handler
        """
        try:
            # Parse request body
            data = self.parse_json_body(handler)
            if data is None:
                self.send_error_response(handler, 400, "Invalid JSON in request body")
                return
            
            # Validate required fields
            required_fields = ['tenant_id', 'vlan_id', 'eip', 'gateway_ip', 'internal_ip']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                self.send_error_response(
                    handler, 400, 
                    f"Missing required fields: {', '.join(missing_fields)}"
                )
                return
            
            # Extract parameters
            tenant_id = data['tenant_id']
            vlan_id = data['vlan_id']
            eip = data['eip']
            gateway_ip = data['gateway_ip']
            internal_ip = data['internal_ip']
            
            # Call core service
            request_id = self.core_service.create_eip(tenant_id, vlan_id, eip, gateway_ip, internal_ip)
            
            if request_id:
                response_data = {
                    "success": True,
                    "request_id": request_id,
                    "message": "EIP creation request submitted"
                }
                self.send_json_response(handler, 200, response_data)
            else:
                self.send_error_response(handler, 500, "Failed to create EIP")
                
        except Exception as e:
            self.logger.error(f"Error handling create EIP: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")
    
    def handle_get_instruction_status(self, handler: BaseHTTPRequestHandler, 
                                    request_id: str, tenant_id: str) -> None:
        """Handle get instruction status request.
        
        Args:
            handler: HTTP request handler
            request_id: Request identifier
            tenant_id: Tenant identifier
        """
        try:
            # Call core service
            instruction_data = self.core_service.get_instruction_status(request_id, tenant_id)
            
            if instruction_data:
                response_data = {
                    "success": True,
                    "instruction": instruction_data
                }
                self.send_json_response(handler, 200, response_data)
            else:
                self.send_error_response(handler, 404, "Instruction not found")
                
        except Exception as e:
            self.logger.error(f"Error getting instruction status: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")
    
    def handle_delete_instruction(self, handler: BaseHTTPRequestHandler, 
                                request_id: str, tenant_id: str) -> None:
        """Handle delete instruction request.
        
        Args:
            handler: HTTP request handler
            request_id: Request identifier
            tenant_id: Tenant identifier
        """
        try:
            # Call core service
            success = self.core_service.delete_instruction(request_id, tenant_id)
            
            if success:
                response_data = {
                    "success": True,
                    "message": "Instruction deleted successfully"
                }
                self.send_json_response(handler, 200, response_data)
            else:
                self.send_error_response(handler, 500, "Failed to delete instruction")
                
        except Exception as e:
            self.logger.error(f"Error deleting instruction: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")


class StatusHandler(BaseHandler):
    """Handler for status-related API endpoints."""
    
    def handle_server_status(self, handler: BaseHTTPRequestHandler) -> None:
        """Handle server status request.
        
        Args:
            handler: HTTP request handler
        """
        try:
            # Get server metadata
            metadata = self.core_service.get_server_metadata()
            
            response_data = {
                "success": True,
                "running": self.core_service.is_running(),
                "metadata": metadata
            }
            
            self.send_json_response(handler, 200, response_data)
            
        except Exception as e:
            self.logger.error(f"Error getting server status: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")
    
    def handle_health_check(self, handler: BaseHTTPRequestHandler) -> None:
        """Handle health check request.
        
        Args:
            handler: HTTP request handler
        """
        try:
            response_data = {
                "success": True,
                "status": "healthy",
                "running": self.core_service.is_running()
            }
            
            self.send_json_response(handler, 200, response_data)
            
        except Exception as e:
            self.logger.error(f"Error in health check: {e}")
            self.send_error_response(handler, 500, f"Internal server error: {e}")
