# V-Switch Agent Configuration
# This file was auto-generated

c = get_config()

c.AgentConfig.agent_id = ''
c.AgentConfig.enable_ssl = False
c.AgentConfig.etcd_host = 'localhost'
c.AgentConfig.etcd_password = ''
c.AgentConfig.etcd_port = 2379
c.AgentConfig.etcd_timeout = 5.0
c.AgentConfig.etcd_username = ''
c.AgentConfig.heartbeat_interval = 30.0
c.AgentConfig.heartbeat_timeout = 90.0
c.AgentConfig.log_file = ''
c.AgentConfig.log_level = 'INFO'
c.AgentConfig.max_retries = 3
c.AgentConfig.node_id = 1
c.AgentConfig.node_register_prefix = '/ovs-cluster/nodes/'
c.AgentConfig.retry_delay = 1.0
c.AgentConfig.ssl_ca_file = ''
c.AgentConfig.ssl_cert_file = ''
c.AgentConfig.ssl_key_file = ''
c.AgentConfig.tenant_watch_prefix = '/ovs-cluster/tenants/'
